# app/db/init_tables_db.py

"""
数据库初始化模块

负责创建数据库目录、初始化数据库表结构等功能。
确保应用程序启动时数据库环境正确配置。
"""

import os

from loguru import logger

from backend.app.db.connection_db import async_engine
from backend.app.models.base_models import Base
from backend.app.config.env import database_settings


async def create_database_tables():
    """创建数据库表结构"""

    if database_settings.db_type == "sqlite":
        # 确保数据目录存在
        data_dir = database_settings.root_path / "data"
        os.makedirs(data_dir, exist_ok=True)

        logger.info(f"创建数据库目录: {data_dir}")


    # 创建所有表
    try:
        async with async_engine.begin() as conn:
            logger.info(f"创建{database_settings.db_type}数据库表...")
            await conn.run_sync(Base.metadata.create_all)
        logger.success(f"{database_settings.db_type}数据库表创建成功")
        

    except Exception as e:
        logger.error(f"创建{database_settings.db_type}数据库表时出错: {e}")
        raise
