
from typing import Dict, Any
from loguru import logger

# 兼容新旧认证库
try:
    from supabase_auth.errors import AuthError
except ImportError:
    try:
        from gotrue.errors import AuthError
    except ImportError:
        # 如果都没有，创建一个基础异常类
        class AuthError(Exception):
            pass

from backend.app.services.supabase_client_service import get_supabase_client



class SupabaseAuthService:
    """Supabase认证服务"""

    def __init__(self, supabase_client=None):
        """
        初始化认证服务

        Args:
            supabase_client: Supabase客户端实例（可选，如果不提供则使用全局实例）
        """

        # 如果没有提供客户端，使用全局实例
        if supabase_client is None:
            supabase_client = get_supabase_client()

        self.client = supabase_client.get_client()
        self.admin_client = supabase_client.get_client(admin=True)

    async def sign_up_with_email(self, email: str, password: str, **kwargs) -> Dict[str, Any]:
        """
        使用邮箱注册用户

        Args:
            email: 用户邮箱
            password: 用户密码
            **kwargs: 其他用户数据

        Returns:
            注册结果
        """
        try:
            response = self.client.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": kwargs
                }
            })

            if response.user:
                logger.info(f"User signed up successfully: {email}")
                return {
                    "success": True,
                    "user": response.user.model_dump(),
                    "session": response.session.model_dump() if response.session else None,
                    "message": "User registered successfully"
                }
            else:
                logger.warning(f"Sign up failed for email: {email}")
                return {
                    "success": False,
                    "message": "Registration failed"
                }

        except AuthError as e:
            logger.error(f"Auth error during sign up: {e}")
            return {
                "success": False,
                "message": str(e)
            }
        except Exception as e:
            logger.error(f"Unexpected error during sign up: {e}")
            return {
                "success": False,
                "message": "An unexpected error occurred"
            }

    async def sign_in_with_email(self, email: str, password: str) -> Dict[str, Any]:
        """
        使用邮箱登录

        Args:
            email: 用户邮箱
            password: 用户密码

        Returns:
            登录结果
        """
        try:
            response = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            if response.user and response.session:
                logger.info(f"User signed in successfully: {email}")
                return {
                    "success": True,
                    "user": response.user.model_dump(),
                    "session": response.session.model_dump(),
                    "access_token": response.session.access_token,
                    "refresh_token": response.session.refresh_token,
                    "message": "Login successful"
                }
            else:
                logger.warning(f"Sign in failed for email: {email}")
                return {
                    "success": False,
                    "message": "Invalid credentials"
                }

        except AuthError as e:
            logger.error(f"Auth error during sign in: {e}")
            return {
                "success": False,
                "message": str(e)
            }
        except Exception as e:
            logger.error(f"Unexpected error during sign in: {e}")
            return {
                "success": False,
                "message": "An unexpected error occurred"
            }

    async def sign_out(self) -> Dict[str, Any]:
        """
        用户登出

        Returns:
            登出结果
        """
        try:
            response = self.client.auth.sign_out()
            logger.info("User signed out successfully")
            return {
                "success": True,
                "message": "Signed out successfully"
            }
        except Exception as e:
            logger.error(f"Error during sign out: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def get_current_user(self) -> Dict[str, Any]:
        """
        获取当前用户信息

        Returns:
            用户信息
        """
        try:
            response = self.client.auth.get_user()
            if response.user:
                logger.info(f"Retrieved current user: {response.user.email}")
                return {
                    "success": True,
                    "user": response.user.model_dump(),
                    "message": "User retrieved successfully"
                }
            else:
                return {
                    "success": False,
                    "message": "No authenticated user"
                }
        except Exception as e:
            logger.error(f"Error getting current user: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def refresh_session(self, refresh_token: str) -> Dict[str, Any]:
        """
        刷新用户会话

        Args:
            refresh_token: 刷新令牌

        Returns:
            刷新结果
        """
        try:
            response = self.client.auth.refresh_session(refresh_token)
            if response.session:
                logger.info("Session refreshed successfully")
                return {
                    "success": True,
                    "session": response.session.model_dump(),
                    "access_token": response.session.access_token,
                    "refresh_token": response.session.refresh_token,
                    "message": "Session refreshed successfully"
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to refresh session"
                }
        except Exception as e:
            logger.error(f"Error refreshing session: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def reset_password(self, email: str) -> Dict[str, Any]:
        """
        发送密码重置邮件

        Args:
            email: 用户邮箱

        Returns:
            重置结果
        """
        try:
            response = self.client.auth.reset_password_email(email)
            logger.info(f"Password reset email sent to: {email}")
            return {
                "success": True,
                "message": "Password reset email sent successfully"
            }
        except Exception as e:
            logger.error(f"Error sending password reset email: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def update_user(self, attributes: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户信息

        Args:
            attributes: 要更新的用户属性

        Returns:
            更新结果
        """
        try:
            response = self.client.auth.update_user(attributes)
            if response.user:
                logger.info(f"User updated successfully: {response.user.email}")
                return {
                    "success": True,
                    "user": response.user.model_dump(),
                    "message": "User updated successfully"
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to update user"
                }
        except Exception as e:
            logger.error(f"Error updating user: {e}")
            return {
                "success": False,
                "message": str(e)
            }


# 创建全局实例（推荐使用方式）
# 这不是严格的单例模式，但在实际使用中已经足够
# 如果需要多个实例（比如连接不同的Supabase项目），可以直接实例化类

auth_service = SupabaseAuthService()  # 使用默认的全局客户端


def get_auth_service() -> SupabaseAuthService:
    """
    获取认证服务实例

    Returns:
        SupabaseAuthService: 认证服务实例
    """
    return auth_service


