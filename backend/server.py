from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import asyncio

from backend.app.config.env import app_settings



@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时的初始化任务


    yield



app = FastAPI(
    title=app_settings.app_name,
    version=app_settings.app_version,
    # lifespan=lifespan,
)


# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=app_settings.app_cors_origins,
    allow_credentials=app_settings.app_cors_credentials,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "message": "Service is running"}


