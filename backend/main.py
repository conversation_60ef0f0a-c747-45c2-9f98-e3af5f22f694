import uvicorn
from backend.server import app, app_settings  # noqa: F401


if __name__ == '__main__':

    # 调试输出
    print(f"Host: {app_settings.app_host}")
    print(f"Port: {app_settings.app_port}")
    print(f"Reload: {app_settings.app_reload}")

    uvicorn.run(
        app='server:app',  # 修正：指向 server.py 中的 app
        host=app_settings.app_host,
        port=app_settings.app_port,
        workers=app_settings.app_workers,
        reload=app_settings.app_reload,
    )