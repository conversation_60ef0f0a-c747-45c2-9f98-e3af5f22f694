
# ===========================================
# 环境变量配置文件 (.env)
# 用于存储敏感信息和环境特定配置
# ===========================================

# ===========================================
# 应用程序配置（环境特定）
# ===========================================
# 注意：大部分应用配置已移至 config.yml
# 这里只保留环境特定的配置

# ===========================================
# 数据库配置（敏感信息）
# ===========================================
DB_HOST=127.0.0.1
DB_PORT=25432
DB_USERNAME=heygo
DB_PASSWORD=heygo01!
DB_DATABASE=heygo


# ===========================================
# Redis 配置（敏感信息）
# ===========================================
REDIS_HOST=127.0.0.1
REDIS_PORT=16379
REDIS_DB=0
REDIS_USERNAME=
REDIS_PASSWORD=

# ===========================================
# JWT 安全配置（敏感信息）
# ===========================================
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_TIME=30
JWT_REFRESH_TOKEN_EXPIRE_TIME=10080

# ===========================================
# Notion API 配置（敏感信息）
# ===========================================
NOTION_API_KEY=
NOTION_DATABASE_ID=

# ===========================================
# Supabase 配置（敏感信息）
# ===========================================
SUPABASE_URL=http://localhost:9080
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNzU1MzYwMDAwLCJleHAiOjE5MTMxMjY0MDB9.wPhnjPuLiTrDMGiUpSyS68ns-8kP6Zt86CDiE_QGvVo
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NTUzNjAwMDAsImV4cCI6MTkxMzEyNjQwMH0.T6n_vIdaUxGb7dV4Qjdm7aG0YcODLbLVTQmQ0Q2PHdM
SUPABASE_JWT_SECRET=fS5UFPylUtABaSwqHNrnqJ5orodgiQzGhbh9ftUL
SUPABASE_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback

# Supabase 数据库直连配置
SUPABASE_DB_HOST=127.0.0.1
SUPABASE_DB_PORT=54325
SUPABASE_DB_USERNAME=postgres
SUPABASE_DB_PASSWORD=HeygoyG7!p2xWq9Vk5&JsZm8LuRd!
SUPABASE_DB_DATABASE=postgres


