-- Supabase RLS策略设置示例
-- 在Supabase SQL编辑器中执行这些语句来设置行级安全

-- ==================== 用户资料表 ====================

-- 创建用户资料表
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    full_name TEXT,
    bio TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 启用RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- 用户只能查看和修改自己的资料
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own profile" ON user_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- 管理员可以查看所有资料
CREATE POLICY "Admins can view all profiles" ON user_profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

-- ==================== 用户设置表 ====================

CREATE TABLE IF NOT EXISTS user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    theme TEXT DEFAULT 'light',
    language TEXT DEFAULT 'zh-CN',
    notifications BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- 用户只能管理自己的设置
CREATE POLICY "Users can manage own settings" ON user_settings
    FOR ALL USING (auth.uid() = user_id);

-- ==================== 用户活动记录表 ====================

CREATE TABLE IF NOT EXISTS user_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE user_activities ENABLE ROW LEVEL SECURITY;

-- 用户只能查看自己的活动记录
CREATE POLICY "Users can view own activities" ON user_activities
    FOR SELECT USING (auth.uid() = user_id);

-- 系统可以插入活动记录（通过service_role）
CREATE POLICY "System can insert activities" ON user_activities
    FOR INSERT WITH CHECK (true);

-- 管理员可以查看所有活动
CREATE POLICY "Admins can view all activities" ON user_activities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

-- ==================== 共享数据表（跨用户访问） ====================

-- 创建文章表（用户可以查看所有文章，但只能编辑自己的）
CREATE TABLE IF NOT EXISTS articles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT,
    status TEXT DEFAULT 'draft', -- draft, published, archived
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE articles ENABLE ROW LEVEL SECURITY;

-- 所有用户可以查看公开的已发布文章
CREATE POLICY "Anyone can view public articles" ON articles
    FOR SELECT USING (is_public = true AND status = 'published');

-- 用户可以查看自己的所有文章
CREATE POLICY "Users can view own articles" ON articles
    FOR SELECT USING (auth.uid() = author_id);

-- 用户只能创建自己的文章
CREATE POLICY "Users can create own articles" ON articles
    FOR INSERT WITH CHECK (auth.uid() = author_id);

-- 用户只能更新自己的文章
CREATE POLICY "Users can update own articles" ON articles
    FOR UPDATE USING (auth.uid() = author_id);

-- 用户只能删除自己的文章
CREATE POLICY "Users can delete own articles" ON articles
    FOR DELETE USING (auth.uid() = author_id);

-- ==================== 评论表（跨用户交互） ====================

CREATE TABLE IF NOT EXISTS comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE comments ENABLE ROW LEVEL SECURITY;

-- 用户可以查看公开文章的评论
CREATE POLICY "Users can view comments on public articles" ON comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM articles 
            WHERE articles.id = comments.article_id 
            AND articles.is_public = true 
            AND articles.status = 'published'
        )
    );

-- 用户可以查看自己文章的所有评论
CREATE POLICY "Authors can view comments on own articles" ON comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM articles 
            WHERE articles.id = comments.article_id 
            AND articles.author_id = auth.uid()
        )
    );

-- 用户可以在公开文章下评论
CREATE POLICY "Users can comment on public articles" ON comments
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM articles 
            WHERE articles.id = comments.article_id 
            AND articles.is_public = true 
            AND articles.status = 'published'
        )
    );

-- 用户只能更新自己的评论
CREATE POLICY "Users can update own comments" ON comments
    FOR UPDATE USING (auth.uid() = author_id);

-- 用户可以删除自己的评论，或者文章作者可以删除文章下的评论
CREATE POLICY "Users can delete own comments or article authors can delete comments" ON comments
    FOR DELETE USING (
        auth.uid() = author_id OR
        EXISTS (
            SELECT 1 FROM articles 
            WHERE articles.id = comments.article_id 
            AND articles.author_id = auth.uid()
        )
    );

-- ==================== 辅助函数 ====================

-- 创建更新时间戳的函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间戳触发器
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at 
    BEFORE UPDATE ON user_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_articles_updated_at 
    BEFORE UPDATE ON articles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at 
    BEFORE UPDATE ON comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==================== 管理员角色设置 ====================

-- 为用户设置管理员角色的函数（只能通过service_role调用）
CREATE OR REPLACE FUNCTION set_user_role(user_id UUID, role TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE auth.users 
    SET raw_user_meta_data = COALESCE(raw_user_meta_data, '{}'::jsonb) || jsonb_build_object('role', role)
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 示例：设置用户为管理员
-- SELECT set_user_role('用户UUID', 'admin');

-- ==================== 测试数据 ====================

-- 插入一些测试数据（在实际使用中可以删除这部分）
/*
-- 注意：这些插入语句需要在有实际用户的情况下执行
-- 可以通过应用程序或者使用service_role执行

INSERT INTO user_profiles (user_id, email, full_name, bio) VALUES
    ('用户UUID1', '<EMAIL>', '用户一', '这是用户一的简介'),
    ('用户UUID2', '<EMAIL>', '用户二', '这是用户二的简介');

INSERT INTO articles (author_id, title, content, status, is_public) VALUES
    ('用户UUID1', '我的第一篇文章', '这是文章内容...', 'published', true),
    ('用户UUID2', '私人笔记', '这是私人内容...', 'draft', false);
*/
