"""
Pydantic 2 Session管理器使用示例

展示Pydantic 2的强大功能：
- 🔍 数据验证和类型检查
- 🛡️ 自动数据清理和转换
- 📊 详细的错误信息
- 🎯 字段验证器
- 🔧 模型配置
"""

import asyncio
import sys
from pathlib import Path
from pydantic import ValidationError

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.app.services.supabase_session_manager_service import get_session_manager
from backend.app.api.v1.schemas import (
    SessionData,
    SessionValidationError,
    SessionExpiredError,
    SessionNotFoundError
)


async def demo_pydantic_validation():
    """演示Pydantic数据验证"""
    print("🔍 Pydantic 2 数据验证演示")
    print("=" * 40)
    
    # 1. 正确的数据
    print("1️⃣ 正确数据验证:")
    try:
        valid_data = {
            "user_id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
            "refresh_token": "refresh_token_example_123456",
            "expires_at": 1735689600,  # 2025年1月1日
            "login_time": 1703153600,  # 2023年12月21日
            "last_activity": 1703153600,
            "user_metadata": {"source": "demo"},
            "session_metadata": {"device": "python_client"}
        }
        
        session = SessionData.model_validate(valid_data)
        print("   ✅ 数据验证成功")
        print(f"   用户: {session.email}")
        print(f"   过期时间: {session.expires_at}")
        print(f"   是否过期: {session.is_expired()}")
        print(f"   距离过期: {session.time_until_expiry()}秒")
        
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 2. 错误的数据
    print("\n2️⃣ 错误数据验证:")
    invalid_data_examples = [
        {
            "name": "无效邮箱",
            "data": {
                "user_id": "123",
                "email": "invalid-email",  # 无效邮箱
                "access_token": "short",   # 太短的token
                "refresh_token": "short",
                "expires_at": -1,          # 负数时间戳
                "login_time": 0,
                "last_activity": 0,
            }
        },
        {
            "name": "缺少必填字段",
            "data": {
                "user_id": "123",
                # 缺少email字段
                "access_token": "valid_token_123456",
            }
        },
        {
            "name": "类型错误",
            "data": {
                "user_id": "123",
                "email": "<EMAIL>",
                "access_token": "valid_token_123456",
                "refresh_token": "valid_refresh_123456",
                "expires_at": "not_a_number",  # 应该是int
                "login_time": 1703153600,
                "last_activity": 1703153600,
            }
        }
    ]
    
    for example in invalid_data_examples:
        print(f"\n   测试: {example['name']}")
        try:
            SessionData.model_validate(example['data'])
            print("   ❌ 应该验证失败但成功了")
        except ValidationError as e:
            print("   ✅ 正确捕获验证错误:")
            for error in e.errors():
                field = " -> ".join(str(loc) for loc in error['loc'])
                print(f"     字段 '{field}': {error['msg']}")


async def demo_pydantic_features():
    """演示Pydantic 2的高级功能"""
    print("\n🎯 Pydantic 2 高级功能演示")
    print("=" * 40)
    
    # 1. 数据清理和转换
    print("1️⃣ 自动数据清理:")
    messy_data = {
        "user_id": "  123e4567-e89b-12d3-a456-426614174000  ",  # 有空格
        "email": "  <EMAIL>  ",  # 大写和空格
        "access_token": "  eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9  ",  # 有空格
        "refresh_token": "  refresh_token_example_123456  ",
        "expires_at": 1735689600,
        "login_time": 1703153600,
        "last_activity": 1703153600,
        "extra_field": "这个字段会被保留"  # 额外字段
    }
    
    try:
        session = SessionData.model_validate(messy_data)
        print("   ✅ 数据自动清理成功")
        print(f"   清理后邮箱: '{session.email}'")  # 应该是小写且无空格
        print(f"   额外字段保留: {hasattr(session, 'extra_field')}")
        
    except ValidationError as e:
        print(f"   ❌ 清理失败: {e}")
    
    # 2. 模型序列化
    print("\n2️⃣ 模型序列化:")
    if 'session' in locals():
        # 完整序列化
        full_dict = session.model_dump()
        print(f"   完整序列化字段数: {len(full_dict)}")
        
        # 排除敏感字段
        safe_dict = session.model_dump(exclude={'access_token', 'refresh_token'})
        print(f"   安全序列化字段数: {len(safe_dict)}")
        
        # 只包含特定字段
        minimal_dict = session.model_dump(include={'user_id', 'email', 'expires_at'})
        print(f"   最小序列化字段数: {len(minimal_dict)}")
        
        # 使用自定义安全方法
        safe_custom = session.to_safe_dict()
        print(f"   自定义安全序列化: access_token = {safe_custom['access_token']}")
    
    # 3. JSON序列化
    print("\n3️⃣ JSON序列化:")
    if 'session' in locals():
        json_str = session.model_dump_json(indent=2)
        print(f"   JSON长度: {len(json_str)} 字符")
        print("   JSON格式正确:", json_str.startswith('{') and json_str.endswith('}'))


async def demo_session_methods():
    """演示SessionData的增强方法"""
    print("\n⚡ SessionData增强方法演示")
    print("=" * 40)
    
    # 创建一个测试session
    current_time = int(asyncio.get_event_loop().time())
    session_data = {
        "user_id": "test-user-123",
        "email": "<EMAIL>",
        "access_token": "test_access_token_123456",
        "refresh_token": "test_refresh_token_123456",
        "expires_at": current_time + 3600,  # 1小时后过期
        "login_time": current_time - 1800,  # 30分钟前登录
        "last_activity": current_time - 300,  # 5分钟前活跃
        "user_metadata": {"role": "user"},
        "session_metadata": {"device": "test"}
    }
    
    try:
        session = SessionData.model_validate(session_data)
        
        print("1️⃣ 时间相关方法:")
        print(f"   是否过期: {session.is_expired()}")
        print(f"   距离过期: {session.time_until_expiry()}秒")
        print(f"   会话持续时间: {session.get_session_duration()}秒")
        print(f"   是否最近活跃: {session.is_recently_active(10)}")  # 10分钟内
        
        print("\n2️⃣ 活动更新:")
        old_activity = session.last_activity
        session.update_activity()
        print(f"   更新前活动时间: {old_activity}")
        print(f"   更新后活动时间: {session.last_activity}")
        print(f"   时间差: {session.last_activity - old_activity}秒")
        
        print("\n3️⃣ 安全信息显示:")
        safe_info = session.to_safe_dict()
        print(f"   安全的access_token: {safe_info['access_token']}")
        print(f"   安全的refresh_token: {safe_info['refresh_token']}")
        
    except ValidationError as e:
        print(f"   ❌ 创建session失败: {e}")


async def demo_error_handling():
    """演示增强的错误处理"""
    print("\n🛡️ 增强错误处理演示")
    print("=" * 40)
    
    # 1. SessionExpiredError
    print("1️⃣ Session过期错误:")
    try:
        expired_session_data = {
            "user_id": "expired-user",
            "email": "<EMAIL>",
            "access_token": "expired_token_123456",
            "refresh_token": "expired_refresh_123456",
            "expires_at": int(asyncio.get_event_loop().time()) - 3600,  # 1小时前过期
            "login_time": int(asyncio.get_event_loop().time()) - 7200,  # 2小时前登录
            "last_activity": int(asyncio.get_event_loop().time()) - 3600,
        }
        
        expired_session = SessionData.model_validate(expired_session_data)
        if expired_session.is_expired():
            raise SessionExpiredError(expired_session)
            
    except SessionExpiredError as e:
        print(f"   ✅ 捕获过期错误: {e.message}")
        print(f"   错误详情: {e.details}")
    
    # 2. SessionNotFoundError
    print("\n2️⃣ Session不存在错误:")
    try:
        raise SessionNotFoundError("non-existent-user-123")
    except SessionNotFoundError as e:
        print(f"   ✅ 捕获不存在错误: {e.message}")
        print(f"   错误详情: {e.details}")
    
    # 3. 验证错误处理
    print("\n3️⃣ 验证错误处理:")
    try:
        invalid_data = {
            "user_id": "",  # 空字符串
            "email": "not-an-email",
            "access_token": "short",
            "refresh_token": "short",
            "expires_at": -1,
            "login_time": 0,
            "last_activity": 0,
        }
        SessionData.model_validate(invalid_data)
    except ValidationError as e:
        validation_errors = []
        for error in e.errors():
            validation_errors.append({
                "field": " -> ".join(str(loc) for loc in error['loc']),
                "error": error['msg'],
                "value": error.get('input')
            })
        
        try:
            raise SessionValidationError(validation_errors)
        except SessionValidationError as ve:
            print(f"   ✅ 捕获验证错误: {ve.message}")
            print("   验证错误详情:")
            for error in ve.details['validation_errors']:
                print(f"     - {error['field']}: {error['error']}")


async def demo_real_world_usage():
    """演示真实世界的使用场景"""
    print("\n🌍 真实世界使用场景演示")
    print("=" * 40)
    
    session_mgr = get_session_manager()
    await session_mgr.initialize()
    
    print("1️⃣ 登录并验证数据:")
    if not session_mgr.is_logged_in():
        result = await session_mgr.login(
            email="<EMAIL>",
            password="heygo01!",
            remember_me=True,
            user_metadata={"demo": "pydantic_example"}
        )
        
        if result["success"]:
            print("   ✅ 登录成功")
        else:
            print(f"   ❌ 登录失败: {result['message']}")
            return
    
    print("\n2️⃣ 获取并验证当前session:")
    if session_mgr._current_session:
        session = session_mgr._current_session
        print(f"   Session类型: {type(session).__name__}")
        print(f"   Pydantic模型: {isinstance(session, SessionData)}")
        print(f"   数据验证: 自动完成")
        
        # 展示Pydantic的强大功能
        print(f"   邮箱格式: {session.email} (自动小写)")
        print(f"   时间验证: 所有时间戳都已验证")
        print(f"   类型安全: 所有字段都有类型检查")
        
        # 序列化测试
        json_data = session.model_dump_json()
        print(f"   JSON序列化: {len(json_data)} 字符")
        
        # 反序列化测试
        import json
        dict_data = json.loads(json_data)
        restored_session = SessionData.model_validate(dict_data)
        print(f"   反序列化成功: {restored_session.email == session.email}")


async def main():
    """主演示函数"""
    print("🚀 Pydantic 2 Session管理器完整演示")
    print("=" * 50)
    
    try:
        # 1. 数据验证演示
        await demo_pydantic_validation()
        
        # 2. 高级功能演示
        await demo_pydantic_features()
        
        # 3. 增强方法演示
        await demo_session_methods()
        
        # 4. 错误处理演示
        await demo_error_handling()
        
        # 5. 真实使用场景
        await demo_real_world_usage()
        
        print("\n🎉 所有演示完成!")
        
        print("\n💡 Pydantic 2 优势总结:")
        print("✅ 强大的数据验证和类型检查")
        print("✅ 自动数据清理和转换")
        print("✅ 详细的错误信息和调试支持")
        print("✅ 灵活的序列化和反序列化")
        print("✅ 高性能和内存效率")
        print("✅ 完美的IDE支持和类型提示")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")


if __name__ == "__main__":
    # 检查依赖
    try:
        import pydantic
        print(f"Pydantic版本: {pydantic.VERSION}")
        if pydantic.VERSION < "2.0.0":
            print("⚠️ 建议使用Pydantic 2.0+")
    except ImportError:
        print("❌ 需要安装Pydantic:")
        print("   uv add pydantic")
        exit(1)
    
    asyncio.run(main())
