"""
验证所有导入是否正确

检查分离后的架构中所有导入路径是否正确
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_imports():
    """测试所有关键导入"""
    print("🔍 验证导入路径")
    print("=" * 40)
    
    import_tests = [
        {
            "name": "Supabase客户端",
            "import": "from backend.app.services.supabase_client import get_supabase_client"
        },
        {
            "name": "认证服务",
            "import": "from backend.app.services.supabase_auth_service import get_auth_service"
        },
        {
            "name": "Session管理器",
            "import": "from backend.app.services.supabase_session_manager import get_session_manager"
        },
        {
            "name": "数据库管理器",
            "import": "from backend.app.services.database_manager import get_database_manager"
        },
        {
            "name": "Schemas - 核心模型",
            "import": "from backend.app.api.v1.schemas import SessionData, SessionState, SessionEvent"
        },
        {
            "name": "Schemas - API模型",
            "import": "from backend.app.api.v1.schemas import LoginRequest, LoginResponse, UserInfoResponse"
        },
        {
            "name": "Schemas - 异常类",
            "import": "from backend.app.api.v1.schemas import SessionManagerError, SessionExpiredError"
        },
        {
            "name": "Schemas - 数据库模型",
            "import": "from backend.app.api.v1.schemas import DatabaseQueryRequest, DatabaseResponse"
        }
    ]
    
    success_count = 0
    total_count = len(import_tests)
    
    for test in import_tests:
        try:
            exec(test["import"])
            print(f"✅ {test['name']}: 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {test['name']}: 导入失败 - {e}")
        except Exception as e:
            print(f"⚠️ {test['name']}: 其他错误 - {e}")
    
    print(f"\n📊 导入测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有导入路径都正确！")
        return True
    else:
        print("❌ 部分导入路径需要修正")
        return False


def test_functionality():
    """测试基本功能"""
    print("\n🔧 验证基本功能")
    print("=" * 40)
    
    try:
        # 测试获取服务实例
        from backend.app.services.supabase_client_service import get_supabase_client
        from backend.app.services.supabase_auth_service import get_auth_service
        from backend.app.services.supabase_session_manager_service import get_session_manager
        
        client = get_supabase_client()
        auth = get_auth_service()
        session_mgr = get_session_manager()
        
        print(f"✅ Supabase客户端: {type(client).__name__}")
        print(f"✅ 认证服务: {type(auth).__name__}")
        print(f"✅ Session管理器: {type(session_mgr).__name__}")
        
        # 测试schemas
        from backend.app.api.v1.schemas import SessionData, LoginRequest
        
        # 创建测试数据
        test_session_data = {
            "user_id": "test-123",
            "email": "<EMAIL>",
            "access_token": "test_token_123456",
            "refresh_token": "test_refresh_123456",
            "expires_at": 1735689600,
            "login_time": 1703153600,
            "last_activity": 1703153600,
        }
        
        session = SessionData.model_validate(test_session_data)
        print(f"✅ SessionData验证: {session.email}")
        
        login_req = LoginRequest(
            email="<EMAIL>",
            password="password123"
        )
        print(f"✅ LoginRequest验证: {login_req.email}")
        
        print("\n🎉 所有功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False


def test_circular_imports():
    """检查循环导入"""
    print("\n🔄 检查循环导入")
    print("=" * 30)
    
    try:
        # 尝试导入所有主要模块
        import backend.app.services.supabase_client_service
        import backend.app.services.supabase_auth_service
        import backend.app.services.supabase_session_manager_service
        import backend.app.services.database_manager
        import backend.app.api.v1.schemas
        
        print("✅ 没有检测到循环导入")
        return True
        
    except ImportError as e:
        if "circular import" in str(e).lower():
            print(f"❌ 检测到循环导入: {e}")
            return False
        else:
            print(f"⚠️ 其他导入错误: {e}")
            return False
    except Exception as e:
        print(f"⚠️ 检查过程中出错: {e}")
        return False


def main():
    """主验证函数"""
    print("🚀 架构导入验证")
    print("=" * 50)
    
    # 1. 测试导入路径
    imports_ok = test_imports()
    
    # 2. 测试基本功能
    functionality_ok = test_functionality()
    
    # 3. 检查循环导入
    no_circular = test_circular_imports()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 验证总结:")
    print(f"   导入路径: {'✅ 正确' if imports_ok else '❌ 有问题'}")
    print(f"   基本功能: {'✅ 正常' if functionality_ok else '❌ 有问题'}")
    print(f"   循环导入: {'✅ 无问题' if no_circular else '❌ 有问题'}")
    
    if imports_ok and functionality_ok and no_circular:
        print("\n🎉 架构验证通过！所有导入路径都正确。")
        print("\n💡 现在可以安全地使用:")
        print("   - uv run python backend/app/services/test.py")
        print("   - uv run python supabase_session_example.py")
        print("   - uv run python pydantic_session_example.py")
        print("   - uv run python schemas_usage_example.py")
    else:
        print("\n❌ 架构验证失败，需要修正导入路径。")
    
    return imports_ok and functionality_ok and no_circular


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
