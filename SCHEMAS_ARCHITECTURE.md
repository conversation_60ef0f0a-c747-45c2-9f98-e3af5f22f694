# Schemas架构分离指南

## 🏗️ 架构概览

我们将Pydantic schemas从服务层分离到专门的schemas层，实现了更清晰的架构分层：

```
backend/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── schemas/           # 📋 数据模型层
│   │       │   ├── __init__.py
│   │       │   └── session_schemas.py
│   │       └── controllers/       # 🎮 控制器层
│   │           └── enhanced_auth_controller.py
│   └── services/                  # 🔧 服务层
│       ├── supabase_client.py
│       ├── supabase_session_manager.py
│       └── database_manager.py
```

## 📋 Schemas层职责

### 1. **数据模型定义**
```python
# backend/app/api/v1/schemas/session_schemas.py
class SessionData(BaseModel):
    """核心session数据模型"""
    user_id: str = Field(..., description="用户ID", min_length=1)
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    # ... 完整的验证逻辑
```

### 2. **API请求/响应模型**
```python
class LoginRequest(BaseModel):
    """登录请求模型"""
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    password: str = Field(..., min_length=6)
    remember_me: bool = Field(default=True)

class LoginResponse(BaseModel):
    """登录响应模型"""
    success: bool
    message: str
    user: Optional[UserInfoResponse] = None
```

### 3. **异常模型**
```python
class SessionManagerError(Exception):
    """结构化异常处理"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
```

## 🔄 导入方式

### 统一导入入口
```python
# 从schemas包统一导入
from backend.app.api.v1.schemas import (
    SessionData,
    LoginRequest,
    LoginResponse,
    SessionManagerError
)
```

### 服务层使用
```python
# backend/app/services/supabase_session_manager_service.py
from backend.app.api.v1.schemas import (
    SessionData,
    SessionState,
    SessionEvent,
    SessionManagerError
)
```

### 控制器层使用
```python
# backend/app/api/v1/controllers/enhanced_auth_controller.py
from backend.app.api.v1.schemas import (
    LoginRequest,
    LoginResponse,
    UserInfoResponse
)
```

## ✨ 分离的优势

### 1. **清晰的职责分离**
- **Schemas层**: 数据定义和验证
- **Services层**: 业务逻辑
- **Controllers层**: API处理

### 2. **更好的复用性**
```python
# 同一个schema可以在多个地方使用
from backend.app.api.v1.schemas import SessionData

# 在服务层使用
class SessionManager:
    def create_session(self) -> SessionData: ...

# 在控制器层使用
@router.post("/login")
async def login() -> LoginResponse: ...

# 在测试中使用
def test_session_validation():
    session = SessionData.model_validate(test_data)
```

### 3. **类型安全**
```python
# IDE会提供完整的类型提示
def process_login(request: LoginRequest) -> LoginResponse:
    # 自动补全和类型检查
    email = request.email  # str
    remember = request.remember_me  # bool
```

### 4. **自动文档生成**
```python
# FastAPI会自动生成API文档
@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    # 自动生成OpenAPI schema
    pass
```

## 🎯 使用示例

### 1. **API控制器中使用**
```python
@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest) -> LoginResponse:
    # 请求自动验证
    result = await session_mgr.login(
        email=request.email,
        password=request.password,
        remember_me=request.remember_me
    )
    
    # 响应自动序列化
    return LoginResponse(
        success=result["success"],
        message=result["message"],
        user=UserInfoResponse(**result["user"]) if result.get("user") else None
    )
```

### 2. **服务层中使用**
```python
class SupabaseSessionManager:
    def __init__(self):
        self._current_session: Optional[SessionData] = None
    
    async def login(self, email: str, password: str) -> Dict[str, Any]:
        # 创建验证过的session数据
        session_data = SessionData(
            user_id=user_id,
            email=email,  # 自动验证和清理
            access_token=token,  # 自动验证长度
            # ...
        )
        self._current_session = session_data
```

### 3. **数据库操作中使用**
```python
async def query_users(request: DatabaseQueryRequest) -> DatabaseResponse:
    # 请求参数自动验证
    result = await db.select(
        table=request.table,  # 已验证非空
        columns=request.columns,
        limit=request.limit  # 已验证范围
    )
    
    # 响应自动构造
    return DatabaseResponse(
        success=True,
        data=result,
        count=len(result)
    )
```

## 🔧 扩展指南

### 1. **添加新的Schema**
```python
# backend/app/api/v1/schemas/user_schemas.py
class UserProfileRequest(BaseModel):
    full_name: str = Field(..., min_length=1, max_length=100)
    bio: Optional[str] = Field(None, max_length=500)
    avatar_url: Optional[str] = Field(None, regex=r'^https?://.+')

# 在__init__.py中导出
from .user_schemas import UserProfileRequest
__all__ = [..., "UserProfileRequest"]
```

### 2. **自定义验证器**
```python
class CustomSchema(BaseModel):
    @field_validator('custom_field')
    @classmethod
    def validate_custom(cls, v):
        # 自定义验证逻辑
        if not custom_validation(v):
            raise ValueError('Custom validation failed')
        return v
```

### 3. **嵌套模型**
```python
class AddressSchema(BaseModel):
    street: str
    city: str
    country: str

class UserSchema(BaseModel):
    name: str
    address: AddressSchema  # 嵌套模型
    addresses: List[AddressSchema]  # 模型列表
```

## 🚀 测试schemas

### 运行schemas演示：
```bash
uv run python schemas_usage_example.py
```

### 运行API服务器：
```bash
# 使用增强的控制器
uv run python -m uvicorn backend.app.api.v1.controllers.enhanced_auth_controller:router --reload
```

## 📊 性能优势

### 1. **验证性能**
- Pydantic 2的C扩展提供极快的验证速度
- 自动缓存验证结果

### 2. **序列化性能**
- 优化的JSON序列化
- 智能的字段排除/包含

### 3. **内存效率**
- 共享schema定义
- 优化的内存布局

## 🎉 总结

通过schemas分离，我们实现了：

✅ **清晰的架构分层** - 数据、业务、API分离
✅ **强大的数据验证** - 自动验证和类型检查
✅ **更好的代码复用** - 统一的数据模型
✅ **类型安全** - 完整的IDE支持
✅ **自动文档** - OpenAPI schema生成
✅ **易于测试** - 独立的数据模型测试
✅ **高性能** - Pydantic 2的优化

这是一个现代化、可维护的Python API架构！
