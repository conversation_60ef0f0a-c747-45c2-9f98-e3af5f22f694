[project]
name = "supabase-test"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.16.4",
    "asyncpg>=0.30.0",
    "bcrypt>=4.3.0",
    "cryptography>=45.0.6",
    "fastapi>=0.116.1",
    "gotrue>=2.12.4",
    "loguru>=0.7.3",
    "passlib[bcrypt]>=1.7.4",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.1",
    "python-jose[cryptography]>=3.5.0",
    "python-multipart>=0.0.20",
    "pyyaml>=6.0.2",
    "redis>=6.4.0",
    "requests>=2.32.5",
    "sqlalchemy[asyncio]>=2.0.42",
    "supabase>=2.18.1",
    "supabase-auth>=2.12.3",
    "uvicorn[standard]>=0.35.0",
]
