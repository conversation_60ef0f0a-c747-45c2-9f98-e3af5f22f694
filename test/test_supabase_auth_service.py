"""
Supabase Auth Service 测试文件

测试 SupabaseAuthService 中的所有主要功能：
1. 用户注册 (sign_up_with_email)
2. 用户登录 (sign_in_with_email)
3. 用户登出 (sign_out)
4. 获取当前用户 (get_current_user)
5. 刷新会话 (refresh_session)
6. 重置密码 (reset_password)
7. 更新用户信息 (update_user)
"""

import sys
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from backend.app.services.supabase_auth_service import get_auth_service, SupabaseAuthService


class AuthServiceTester:
    """认证服务测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.auth_service = get_auth_service()
        self.test_email = "<EMAIL>"
        self.test_password = "test_password_123"
        self.test_user_data = {
            "full_name": "Test User",
            "age": 25,
            "role": "tester"
        }
        self.current_session: Optional[Dict[str, Any]] = None
        
        logger.info("Auth Service Tester initialized")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("Starting comprehensive auth service tests")
        print("\n" + "="*60)
        print("🧪 SUPABASE AUTH SERVICE COMPREHENSIVE TESTS")
        print("="*60)
        
        try:
            # 1. 测试用户注册
            await self.test_user_signup()
            
            # 2. 测试用户登录
            await self.test_user_signin()
            
            # 3. 测试获取当前用户
            await self.test_get_current_user()
            
            # 4. 测试更新用户信息
            await self.test_update_user()
            
            # 5. 测试刷新会话
            await self.test_refresh_session()
            
            # 6. 测试重置密码
            await self.test_reset_password()
            
            # 7. 测试用户登出
            await self.test_user_signout()
            
            # 8. 测试错误情况
            await self.test_error_cases()
            
            print("\n" + "="*60)
            print("✅ ALL TESTS COMPLETED")
            print("="*60)
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            print(f"\n❌ Test suite failed: {e}")
    
    async def test_user_signup(self):
        """测试用户注册"""
        print("\n📝 Testing User Signup...")
        print("-" * 40)
        
        try:
            result = await self.auth_service.sign_up_with_email(
                email=self.test_email,
                password=self.test_password,
                **self.test_user_data
            )
            
            print(f"✅ Signup Result: {result['success']}")
            print(f"📧 Message: {result['message']}")
            
            if result['success'] and result.get('user'):
                print(f"👤 User ID: {result['user']['id']}")
                print(f"📧 Email: {result['user']['email']}")
                print(f"✅ Email Confirmed: {result['user'].get('email_confirmed_at', 'Not confirmed')}")
            
            logger.info(f"Signup test completed: {result['success']}")
            
        except Exception as e:
            logger.error(f"Signup test failed: {e}")
            print(f"❌ Signup test failed: {e}")
    
    async def test_user_signin(self):
        """测试用户登录"""
        print("\n🔐 Testing User Signin...")
        print("-" * 40)
        
        try:
            result = await self.auth_service.sign_in_with_email(
                email=self.test_email,
                password=self.test_password
            )
            
            print(f"✅ Signin Result: {result['success']}")
            print(f"📧 Message: {result['message']}")
            
            if result['success']:
                self.current_session = result
                print(f"👤 User ID: {result['user']['id']}")
                print(f"📧 Email: {result['user']['email']}")
                print(f"🎫 Access Token: {result['access_token'][:20]}...")
                print(f"🔄 Refresh Token: {result['refresh_token'][:20]}...")
                
                # 检查session信息
                if result.get('session'):
                    expires_at = result['session'].get('expires_at')
                    print(f"⏰ Session Expires At: {expires_at}")
            
            logger.info(f"Signin test completed: {result['success']}")
            
        except Exception as e:
            logger.error(f"Signin test failed: {e}")
            print(f"❌ Signin test failed: {e}")
    
    async def test_get_current_user(self):
        """测试获取当前用户"""
        print("\n👤 Testing Get Current User...")
        print("-" * 40)
        
        try:
            result = await self.auth_service.get_current_user()
            
            print(f"✅ Get User Result: {result['success']}")
            print(f"📧 Message: {result['message']}")
            
            if result['success'] and result.get('user'):
                user = result['user']
                print(f"👤 User ID: {user['id']}")
                print(f"📧 Email: {user['email']}")
                print(f"📅 Created At: {user.get('created_at', 'N/A')}")
                print(f"🔄 Updated At: {user.get('updated_at', 'N/A')}")
                
                # 显示用户元数据
                if user.get('user_metadata'):
                    print(f"📋 User Metadata: {user['user_metadata']}")
            
            logger.info(f"Get current user test completed: {result['success']}")
            
        except Exception as e:
            logger.error(f"Get current user test failed: {e}")
            print(f"❌ Get current user test failed: {e}")
    
    async def test_update_user(self):
        """测试更新用户信息"""
        print("\n✏️ Testing Update User...")
        print("-" * 40)
        
        try:
            update_data = {
                "data": {
                    "full_name": "Updated Test User",
                    "age": 26,
                    "last_updated": "2024-01-01"
                }
            }
            
            result = await self.auth_service.update_user(update_data)
            
            print(f"✅ Update Result: {result['success']}")
            print(f"📧 Message: {result['message']}")
            
            if result['success'] and result.get('user'):
                user = result['user']
                print(f"👤 User ID: {user['id']}")
                print(f"📧 Email: {user['email']}")
                if user.get('user_metadata'):
                    print(f"📋 Updated Metadata: {user['user_metadata']}")
            
            logger.info(f"Update user test completed: {result['success']}")
            
        except Exception as e:
            logger.error(f"Update user test failed: {e}")
            print(f"❌ Update user test failed: {e}")
    
    async def test_refresh_session(self):
        """测试刷新会话"""
        print("\n🔄 Testing Refresh Session...")
        print("-" * 40)
        
        if not self.current_session or not self.current_session.get('refresh_token'):
            print("⚠️ No active session to refresh, skipping test")
            return
        
        try:
            refresh_token = self.current_session['refresh_token']
            result = await self.auth_service.refresh_session(refresh_token)
            
            print(f"✅ Refresh Result: {result['success']}")
            print(f"📧 Message: {result['message']}")
            
            if result['success']:
                print(f"🎫 New Access Token: {result['access_token'][:20]}...")
                print(f"🔄 New Refresh Token: {result['refresh_token'][:20]}...")
                
                if result.get('session'):
                    expires_at = result['session'].get('expires_at')
                    print(f"⏰ New Expires At: {expires_at}")
                
                # 更新当前session
                self.current_session.update(result)
            
            logger.info(f"Refresh session test completed: {result['success']}")
            
        except Exception as e:
            logger.error(f"Refresh session test failed: {e}")
            print(f"❌ Refresh session test failed: {e}")
    
    async def test_reset_password(self):
        """测试重置密码"""
        print("\n🔑 Testing Reset Password...")
        print("-" * 40)
        
        try:
            result = await self.auth_service.reset_password(self.test_email)
            
            print(f"✅ Reset Password Result: {result['success']}")
            print(f"📧 Message: {result['message']}")
            
            if result['success']:
                print("📧 Password reset email should be sent (check email)")
            
            logger.info(f"Reset password test completed: {result['success']}")
            
        except Exception as e:
            logger.error(f"Reset password test failed: {e}")
            print(f"❌ Reset password test failed: {e}")
    
    async def test_user_signout(self):
        """测试用户登出"""
        print("\n🚪 Testing User Signout...")
        print("-" * 40)
        
        try:
            result = await self.auth_service.sign_out()
            
            print(f"✅ Signout Result: {result['success']}")
            print(f"📧 Message: {result['message']}")
            
            if result['success']:
                self.current_session = None
                print("👋 User successfully signed out")
            
            logger.info(f"Signout test completed: {result['success']}")
            
        except Exception as e:
            logger.error(f"Signout test failed: {e}")
            print(f"❌ Signout test failed: {e}")
    
    async def test_error_cases(self):
        """测试错误情况"""
        print("\n⚠️ Testing Error Cases...")
        print("-" * 40)
        
        # 测试无效邮箱登录
        print("1️⃣ Testing invalid email login...")
        try:
            result = await self.auth_service.sign_in_with_email(
                email="<EMAIL>",
                password="wrong_password"
            )
            print(f"   Result: {result['success']} - {result['message']}")
        except Exception as e:
            print(f"   Exception: {e}")
        
        # 测试空密码
        print("2️⃣ Testing empty password...")
        try:
            result = await self.auth_service.sign_in_with_email(
                email=self.test_email,
                password=""
            )
            print(f"   Result: {result['success']} - {result['message']}")
        except Exception as e:
            print(f"   Exception: {e}")
        
        # 测试无效刷新令牌
        print("3️⃣ Testing invalid refresh token...")
        try:
            result = await self.auth_service.refresh_session("invalid_refresh_token")
            print(f"   Result: {result['success']} - {result['message']}")
        except Exception as e:
            print(f"   Exception: {e}")
        
        logger.info("Error cases test completed")


async def main():
    """主函数"""
    tester = AuthServiceTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
