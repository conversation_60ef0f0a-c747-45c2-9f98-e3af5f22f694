# Supabase Auth Service 测试文件

这个目录包含了用于测试 Supabase Auth Service 功能的测试文件。

## 📁 文件结构

```
test/
├── README.md                           # 本文件，测试说明
├── run_tests.py                        # 测试运行器，统一管理所有测试
├── test_supabase_connection.py         # Supabase 连接和配置测试
├── test_auth_basic.py                  # 基础认证功能测试
└── test_supabase_auth_service.py       # 完整的认证服务测试
```

## 🧪 测试文件说明

### 1. `test_supabase_connection.py`
**连接和配置测试**
- ✅ 检查 Supabase 配置是否正确
- ✅ 测试客户端初始化
- ✅ 验证网络连接
- ✅ 检查环境依赖

**适用场景：** 首次设置时验证配置是否正确

### 2. `test_auth_basic.py`
**基础认证功能测试**
- ✅ 用户注册 (sign_up_with_email)
- ✅ 用户登录 (sign_in_with_email)
- ✅ 获取当前用户 (get_current_user)
- ✅ 刷新会话 (refresh_session)
- ✅ 用户登出 (sign_out)
- ✅ 错误处理测试

**适用场景：** 快速验证核心认证功能是否正常

### 3. `test_supabase_auth_service.py`
**完整的认证服务测试**
- ✅ 所有认证功能的详细测试
- ✅ 用户信息更新 (update_user)
- ✅ 密码重置 (reset_password)
- ✅ 详细的错误场景测试
- ✅ 会话管理测试

**适用场景：** 全面测试所有认证服务功能

### 4. `run_tests.py`
**测试运行器**
- ✅ 统一运行所有测试
- ✅ 交互式菜单选择
- ✅ 测试结果统计
- ✅ 依赖检查

## 🚀 使用方法

### 方法一：使用测试运行器（推荐）

```bash
# 进入项目根目录
cd /path/to/supabase_test

# 运行交互式菜单（默认）
python test/run_tests.py

# 运行所有测试
python test/run_tests.py --all

# 运行指定测试
python test/run_tests.py test_auth_basic.py
```

### 方法二：直接运行单个测试

```bash
# 连接测试
python test/test_supabase_connection.py

# 基础功能测试
python test/test_auth_basic.py

# 完整功能测试
python test/test_supabase_auth_service.py
```

## ⚙️ 测试前准备

### 1. 确保 Supabase 服务运行
```bash
# 如果使用 Docker
docker-compose up -d

# 检查服务状态
curl http://localhost:9080/health
```

### 2. 检查环境配置
确保 `backend/.env` 文件中的 Supabase 配置正确：
```env
SUPABASE_URL=http://localhost:9080
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. 安装依赖
```bash
pip install supabase gotrue loguru pydantic asyncio
```

## 📊 测试结果解读

### ✅ 成功标志
- `✅ 成功` - 功能正常工作
- `ℹ️ 信息` - 正常的信息提示
- `⚠️ 警告` - 非关键问题，功能可能受限

### ❌ 失败标志
- `❌ 失败` - 功能无法正常工作
- `异常` - 发生了未预期的错误

## 🔧 常见问题

### 1. 连接失败
```
❌ 网络连接测试失败: Connection refused
```
**解决方案：**
- 检查 Supabase 服务是否启动
- 验证 URL 和端口配置
- 检查防火墙设置

### 2. 认证失败
```
❌ 登录异常: Invalid login credentials
```
**解决方案：**
- 检查用户是否已注册
- 验证邮箱和密码是否正确
- 确认 Supabase Auth 配置

### 3. 权限错误
```
⚠️ 数据库查询测试: permission denied
```
**解决方案：**
- 这通常是正常的，表示 RLS 策略生效
- 检查 Service Role Key 配置
- 验证数据库权限设置

### 4. 依赖缺失
```
❌ supabase (缺失)
```
**解决方案：**
```bash
pip install supabase gotrue loguru pydantic
```

## 📝 测试数据

测试使用的默认数据：
- **测试邮箱：** `<EMAIL>`
- **测试密码：** `test123456`
- **用户数据：** `{"full_name": "Test User", "age": 25}`

**注意：** 测试会创建真实的用户数据，建议在开发环境中运行。

## 🎯 测试建议

### 首次使用
1. 先运行 `test_supabase_connection.py` 验证配置
2. 再运行 `test_auth_basic.py` 测试基础功能
3. 最后运行完整测试验证所有功能

### 日常开发
- 修改认证相关代码后运行对应测试
- 使用 `run_tests.py --all` 进行回归测试

### 生产部署前
- 运行所有测试确保功能正常
- 检查测试覆盖率
- 验证错误处理逻辑

## 📞 支持

如果遇到问题：
1. 检查日志输出中的详细错误信息
2. 验证 Supabase 服务状态
3. 确认环境配置正确
4. 查看 Supabase 文档获取更多帮助
