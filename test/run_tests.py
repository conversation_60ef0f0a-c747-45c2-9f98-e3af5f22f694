"""
测试运行器

统一运行所有 Supabase Auth Service 相关测试
"""

import sys
import asyncio
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_dir = Path(__file__).parent
        self.test_files = [
            "test_supabase_connection.py",
            "test_auth_basic.py", 
            "test_supabase_auth_service.py"
        ]
        
    def print_header(self, title: str):
        """打印标题"""
        print("\n" + "=" * 60)
        print(f"🧪 {title}")
        print("=" * 60)
    
    def print_separator(self):
        """打印分隔符"""
        print("\n" + "-" * 60)
    
    async def run_single_test(self, test_file: str) -> bool:
        """运行单个测试文件"""
        test_path = self.test_dir / test_file
        
        if not test_path.exists():
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        self.print_header(f"运行测试: {test_file}")
        
        try:
            # 使用 subprocess 运行测试文件
            process = await asyncio.create_subprocess_exec(
                sys.executable, str(test_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(project_root)
            )
            
            stdout, stderr = await process.communicate()
            
            # 打印输出
            if stdout:
                print(stdout.decode('utf-8', errors='ignore'))
            
            if stderr:
                print("STDERR:")
                print(stderr.decode('utf-8', errors='ignore'))
            
            success = process.returncode == 0
            
            if success:
                print(f"✅ {test_file} 测试完成")
            else:
                print(f"❌ {test_file} 测试失败 (返回码: {process.returncode})")
            
            return success
            
        except Exception as e:
            print(f"❌ 运行 {test_file} 时发生异常: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.print_header("Supabase Auth Service 测试套件")
        
        print("📋 测试计划:")
        for i, test_file in enumerate(self.test_files, 1):
            print(f"   {i}. {test_file}")
        
        results = {}
        
        for test_file in self.test_files:
            self.print_separator()
            success = await self.run_single_test(test_file)
            results[test_file] = success
            
            # 在测试之间稍作停顿
            await asyncio.sleep(1)
        
        # 打印总结
        self.print_separator()
        self.print_header("测试结果总结")
        
        total_tests = len(results)
        passed_tests = sum(1 for success in results.values() if success)
        failed_tests = total_tests - passed_tests
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests} ✅")
        print(f"   失败: {failed_tests} ❌")
        print(f"   成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_file, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            print(f"   {test_file}: {status}")
        
        if failed_tests == 0:
            print(f"\n🎉 所有测试都通过了！")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查上面的错误信息")
        
        return failed_tests == 0
    
    async def run_interactive_menu(self):
        """运行交互式菜单"""
        while True:
            self.print_header("Supabase Auth Service 测试菜单")
            
            print("请选择要运行的测试:")
            print("0. 运行所有测试")
            
            for i, test_file in enumerate(self.test_files, 1):
                print(f"{i}. {test_file}")
            
            print("q. 退出")
            
            choice = input("\n请输入选择 (0-{} 或 q): ".format(len(self.test_files))).strip().lower()
            
            if choice == 'q':
                print("👋 退出测试")
                break
            elif choice == '0':
                await self.run_all_tests()
            elif choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(self.test_files):
                    await self.run_single_test(self.test_files[index])
                else:
                    print("❌ 无效选择")
            else:
                print("❌ 无效选择")
            
            input("\n按 Enter 继续...")


def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'supabase',
        'loguru',
        'pydantic',
        'asyncio'
    ]

    # 检查认证库（优先使用新的 supabase_auth）
    auth_packages = ['supabase_auth', 'gotrue']
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (缺失)")
            missing_packages.append(package)

    # 检查认证库
    auth_available = False
    for auth_package in auth_packages:
        try:
            __import__(auth_package)
            if auth_package == 'gotrue':
                print(f"   ⚠️ {auth_package} (已弃用，建议使用 supabase_auth)")
            else:
                print(f"   ✅ {auth_package}")
            auth_available = True
            break
        except ImportError:
            continue

    if not auth_available:
        print(f"   ❌ 认证库 (缺失 supabase_auth 或 gotrue)")
        missing_packages.extend(auth_packages)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖都已安装")
    return True


async def main():
    """主函数"""
    print("🚀 Supabase Auth Service 测试运行器")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    runner = TestRunner()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--all":
            # 运行所有测试
            await runner.run_all_tests()
        elif sys.argv[1] == "--interactive":
            # 交互式菜单
            await runner.run_interactive_menu()
        elif sys.argv[1] in runner.test_files:
            # 运行指定测试
            await runner.run_single_test(sys.argv[1])
        else:
            print("❌ 无效参数")
            print("用法:")
            print("  python run_tests.py --all          # 运行所有测试")
            print("  python run_tests.py --interactive  # 交互式菜单")
            print("  python run_tests.py <test_file>    # 运行指定测试")
    else:
        # 默认运行交互式菜单
        await runner.run_interactive_menu()


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试运行器发生错误: {e}")
        logger.exception("Test runner error")
