"""
Supabase Auth Service 基础测试

简化版测试，专注于核心功能验证
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from backend.app.services.supabase_auth_service import get_auth_service


async def test_basic_auth():
    """测试基本认证功能"""
    print("\n=== 🧪 基本认证功能测试 ===")
    
    auth_service = get_auth_service()
    
    # 测试用户信息
    test_email = "<EMAIL>"
    test_password = "usertest123"
    
    print(f"📧 测试邮箱: {test_email}")
    print(f"🔑 测试密码: {test_password}")
    
    # 1. 测试用户注册
    print("\n1️⃣ 测试用户注册...")
    try:
        signup_result = await auth_service.sign_up_with_email(
            email=test_email,
            password=test_password,
            full_name="Test User"
        )
        
        print(f"   注册结果: {'✅ 成功' if signup_result['success'] else '❌ 失败'}")
        print(f"   消息: {signup_result['message']}")
        
        if signup_result['success'] and signup_result.get('user'):
            print(f"   用户ID: {signup_result['user']['id']}")
            print(f"   邮箱: {signup_result['user']['email']}")
        
    except Exception as e:
        print(f"   ❌ 注册异常: {e}")
    
    # 2. 测试用户登录
    print("\n2️⃣ 测试用户登录...")
    session_data = None
    try:
        signin_result = await auth_service.sign_in_with_email(
            email=test_email,
            password=test_password
        )
        
        print(f"   登录结果: {'✅ 成功' if signin_result['success'] else '❌ 失败'}")
        print(f"   消息: {signin_result['message']}")
        
        if signin_result['success']:
            session_data = signin_result
            print(f"   用户ID: {signin_result['user']['id']}")
            print(f"   邮箱: {signin_result['user']['email']}")
            print(f"   访问令牌: {signin_result['access_token'][:20]}...")
        
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
    
    # 3. 测试获取当前用户
    print("\n3️⃣ 测试获取当前用户...")
    try:
        user_result = await auth_service.get_current_user()
        
        print(f"   获取结果: {'✅ 成功' if user_result['success'] else '❌ 失败'}")
        print(f"   消息: {user_result['message']}")
        
        if user_result['success'] and user_result.get('user'):
            user = user_result['user']
            print(f"   用户ID: {user['id']}")
            print(f"   邮箱: {user['email']}")
            print(f"   创建时间: {user.get('created_at', 'N/A')}")
        
    except Exception as e:
        print(f"   ❌ 获取用户异常: {e}")
    
    # 4. 测试刷新会话（如果有session）
    if session_data and session_data.get('refresh_token'):
        print("\n4️⃣ 测试刷新会话...")
        try:
            refresh_result = await auth_service.refresh_session(
                session_data['refresh_token']
            )
            
            print(f"   刷新结果: {'✅ 成功' if refresh_result['success'] else '❌ 失败'}")
            print(f"   消息: {refresh_result['message']}")
            
            if refresh_result['success']:
                print(f"   新访问令牌: {refresh_result['access_token'][:20]}...")
        
        except Exception as e:
            print(f"   ❌ 刷新会话异常: {e}")
    
    # 5. 测试用户登出
    print("\n5️⃣ 测试用户登出...")
    try:
        signout_result = await auth_service.sign_out()
        
        print(f"   登出结果: {'✅ 成功' if signout_result['success'] else '❌ 失败'}")
        print(f"   消息: {signout_result['message']}")
        
    except Exception as e:
        print(f"   ❌ 登出异常: {e}")
    
    print("\n=== ✅ 基础测试完成 ===")


async def test_error_handling():
    """测试错误处理"""
    print("\n=== ⚠️ 错误处理测试 ===")
    
    auth_service = get_auth_service()
    
    # 1. 测试无效邮箱登录
    print("\n1️⃣ 测试无效邮箱登录...")
    try:
        result = await auth_service.sign_in_with_email(
            email="<EMAIL>",
            password="wrongpassword"
        )
        print(f"   结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"   消息: {result['message']}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 2. 测试空密码
    print("\n2️⃣ 测试空密码...")
    try:
        result = await auth_service.sign_in_with_email(
            email="<EMAIL>",
            password=""
        )
        print(f"   结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"   消息: {result['message']}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 3. 测试无效刷新令牌
    print("\n3️⃣ 测试无效刷新令牌...")
    try:
        result = await auth_service.refresh_session("invalid_token")
        print(f"   结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"   消息: {result['message']}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print("\n=== ✅ 错误处理测试完成 ===")


async def test_connection():
    """测试连接"""
    print("\n=== 🔗 连接测试 ===")
    
    try:
        auth_service = get_auth_service()
        print("✅ 认证服务初始化成功")
        
        # 尝试获取当前用户（测试连接）
        result = await auth_service.get_current_user()
        print(f"✅ Supabase连接测试: {'成功' if result else '失败'}")
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
    
    print("=== ✅ 连接测试完成 ===")


async def main():
    """主函数"""
    print("🚀 开始 Supabase Auth Service 测试")
    print("=" * 50)
    
    # 1. 连接测试
    await test_connection()
    
    # 2. 基础功能测试
    await test_basic_auth()
    
    # 3. 错误处理测试
    await test_error_handling()
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成!")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    asyncio.run(main())
