# 依赖更新说明

## ⚠️ 重要提醒：GoTrue 包已弃用

在运行测试时，你可能会看到以下警告：

```
DeprecationWarning: The `gotrue` package is deprecated, is not going to receive updates in the future. Please, use `supabase_auth` instead.
```

## 🔄 解决方案

### 方案一：安装新的认证库（推荐）

```bash
pip install supabase-auth
```

### 方案二：继续使用 gotrue（临时方案）

如果暂时无法升级，可以忽略警告，但建议尽快迁移到新库。

```bash
# 抑制警告（不推荐）
python -W ignore::DeprecationWarning test/test_supabase_connection.py
```

## 📦 完整的依赖安装

### 使用新的认证库

```bash
pip install supabase supabase-auth loguru pydantic asyncio httpx
```

### 使用旧的认证库（临时）

```bash
pip install supabase gotrue loguru pydantic asyncio httpx
```

## 🔍 检查当前依赖

运行以下命令检查你的依赖状态：

```bash
python test/run_tests.py
```

测试运行器会自动检查依赖并给出相应提示。

## 📋 依赖状态说明

- ✅ **supabase_auth** - 新的认证库（推荐）
- ⚠️ **gotrue** - 旧的认证库（已弃用但仍可用）
- ❌ **缺失** - 需要安装

## 🚀 迁移建议

1. **立即可行：** 安装 `supabase-auth` 包
2. **长期规划：** 更新代码以使用新的 API（如果有变化）
3. **测试验证：** 运行所有测试确保功能正常

## 💡 注意事项

- 新的 `supabase_auth` 包可能与 `gotrue` 有 API 差异
- 建议在开发环境中先测试新包的兼容性
- 如果遇到兼容性问题，可以暂时继续使用 `gotrue`

## 📞 获取帮助

如果在依赖更新过程中遇到问题：

1. 查看 Supabase 官方文档
2. 检查 `supabase-auth` 包的文档
3. 在 GitHub 上查看相关 issue
4. 考虑在 Supabase 社区寻求帮助
