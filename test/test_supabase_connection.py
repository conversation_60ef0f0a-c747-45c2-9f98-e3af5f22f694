"""
Supabase 连接测试

测试 Supabase 客户端连接和基本配置
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from backend.app.services.supabase_client_service import get_supabase_client
from backend.app.config.env import supabase_settings


def test_config():
    """测试配置"""
    print("\n=== 📋 配置测试 ===")
    
    print(f"Supabase URL: {supabase_settings.supabase_url}")
    print(f"Anon Key: {supabase_settings.supabase_anon_key[:20]}..." if supabase_settings.supabase_anon_key else "Anon Key: 未设置")
    print(f"Service Role Key: {supabase_settings.supabase_service_role_key[:20]}..." if supabase_settings.supabase_service_role_key else "Service Role Key: 未设置")
    
    # 检查必要配置
    if not supabase_settings.supabase_url:
        print("❌ Supabase URL 未配置")
        return False
    
    if not supabase_settings.supabase_anon_key:
        print("❌ Supabase Anon Key 未配置")
        return False
    
    print("✅ 基本配置检查通过")
    return True


def test_client_initialization():
    """测试客户端初始化"""
    print("\n=== 🔧 客户端初始化测试 ===")
    
    try:
        # 获取客户端服务
        client_service = get_supabase_client()
        print("✅ 客户端服务获取成功")
        
        # 获取普通客户端
        client = client_service.get_client()
        print("✅ 普通客户端获取成功")
        print(f"   客户端类型: {type(client)}")
        
        # 获取管理员客户端
        admin_client = client_service.get_client(admin=True)
        if admin_client:
            print("✅ 管理员客户端获取成功")
            print(f"   管理员客户端类型: {type(admin_client)}")
        else:
            print("⚠️ 管理员客户端未配置（Service Role Key 可能未设置）")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端初始化失败: {e}")
        return False


async def test_basic_operations():
    """测试基本操作"""
    print("\n=== 🔍 基本操作测试 ===")
    
    try:
        client_service = get_supabase_client()
        client = client_service.get_client()
        
        # 测试认证状态
        print("1️⃣ 测试认证状态...")
        try:
            user_response = client.auth.get_user()
            if user_response.user:
                print(f"   ✅ 当前用户: {user_response.user.email}")
            else:
                print("   ℹ️ 当前无登录用户")
        except Exception as e:
            print(f"   ℹ️ 获取用户状态: {e}")
        
        # 测试数据库连接（尝试查询一个系统表）
        print("2️⃣ 测试数据库连接...")
        try:
            # 尝试查询 auth.users 表（需要适当权限）
            response = client.table('auth.users').select('count').limit(1).execute()
            print("   ✅ 数据库连接正常")
        except Exception as e:
            print(f"   ⚠️ 数据库查询测试: {e}")
            # 这是正常的，因为可能没有权限访问 auth.users
        
        # 测试存储连接
        print("3️⃣ 测试存储连接...")
        try:
            buckets = client.storage.list_buckets()
            print(f"   ✅ 存储连接正常，发现 {len(buckets)} 个存储桶")
            for bucket in buckets:
                print(f"      - {bucket.name}")
        except Exception as e:
            print(f"   ⚠️ 存储连接测试: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本操作测试失败: {e}")
        return False


async def test_auth_endpoints():
    """测试认证端点"""
    print("\n=== 🔐 认证端点测试 ===")
    
    try:
        client_service = get_supabase_client()
        client = client_service.get_client()
        
        # 测试认证配置
        print("1️⃣ 测试认证配置...")
        try:
            # 尝试获取认证设置（这可能需要特殊权限）
            settings = client.auth.get_settings()
            if settings:
                print("   ✅ 认证设置获取成功")
            else:
                print("   ℹ️ 无法获取认证设置")
        except Exception as e:
            print(f"   ℹ️ 认证设置: {e}")
        
        # 测试会话状态
        print("2️⃣ 测试会话状态...")
        try:
            session = client.auth.get_session()
            if session:
                print("   ✅ 当前有活跃会话")
                print(f"      用户: {session.user.email if session.user else 'N/A'}")
            else:
                print("   ℹ️ 当前无活跃会话")
        except Exception as e:
            print(f"   ℹ️ 会话状态: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证端点测试失败: {e}")
        return False


def test_environment():
    """测试环境信息"""
    print("\n=== 🌍 环境信息测试 ===")
    
    try:
        import supabase
        print(f"✅ Supabase Python 版本: {supabase.__version__}")
    except:
        print("❌ 无法获取 Supabase Python 版本")
    
    try:
        import supabase_auth
        print(f"✅ Supabase Auth 版本: {supabase_auth.__version__}")
    except:
        try:
            import gotrue
            print(f"⚠️ GoTrue 版本: {gotrue.__version__} (已弃用，建议使用 supabase_auth)")
        except:
            print("❌ 无法获取认证库版本")
    
    try:
        import httpx
        print(f"✅ HTTPX 版本: {httpx.__version__}")
    except:
        print("❌ 无法获取 HTTPX 版本")
    
    # 检查网络连接
    print("\n🌐 网络连接测试...")
    try:
        import httpx
        with httpx.Client() as client:
            response = client.get(supabase_settings.supabase_url, timeout=10)
            print(f"✅ Supabase 服务器响应: {response.status_code}")
    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")


async def main():
    """主函数"""
    print("🚀 开始 Supabase 连接测试")
    print("=" * 50)
    
    # 1. 配置测试
    config_ok = test_config()
    
    if not config_ok:
        print("\n❌ 配置测试失败，请检查环境变量配置")
        return
    
    # 2. 环境测试
    test_environment()
    
    # 3. 客户端初始化测试
    client_ok = test_client_initialization()
    
    if not client_ok:
        print("\n❌ 客户端初始化失败")
        return
    
    # 4. 基本操作测试
    await test_basic_operations()
    
    # 5. 认证端点测试
    await test_auth_endpoints()
    
    print("\n" + "=" * 50)
    print("🎉 连接测试完成!")
    print("\n💡 提示:")
    print("   - 如果看到权限相关的警告，这是正常的")
    print("   - 确保 Supabase 服务正在运行")
    print("   - 检查防火墙和网络设置")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    asyncio.run(main())
